import { router, useLocalSearchParams } from 'expo-router';
import React from 'react';
import { SafeAreaView, StyleSheet, View } from 'react-native';

import { ThreeXUIConfigForm } from '~/panels/3x-ui';
import { SUIConfigForm } from '~/panels/s-ui';
import { Text } from '@/components/ui/text';
import { XUIConfigForm } from '~/panels/x-ui';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppStore } from '@/lib/store';
import { ConfigFormData, ConfigType } from '@/lib/types';

export default function ConfigFormScreen() {
  const { t } = useTranslation();
  const backgroundColor = useThemeColor({}, 'background');
  const { addConfig } = useAppStore();
  const { configType } = useLocalSearchParams<{ configType: ConfigType }>();

  const handleFormSubmit = async (formData: ConfigFormData) => {
    if (!configType) return;

    try {
      // 使用store的addConfig方法
      await addConfig(formData, configType);

      // 关闭模态框并返回主页
      router.back();
    } catch (error) {
      console.error('Failed to add config:', error);
      // 这里可以添加错误提示
    }
  };



  if (!configType) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <View style={styles.errorContainer}>
          <Text className="text-lg text-center">{t('common.error')}</Text>
          <Text className="text-sm text-muted-foreground text-center mt-2">
            Missing configType parameter
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <View style={styles.content}>
        {configType === 's-ui' && (
          <SUIConfigForm
            onSubmit={handleFormSubmit}
          />
        )}
        {configType === 'x-ui' && (
          <XUIConfigForm
            onSubmit={handleFormSubmit}
          />
        )}
        {configType === '3x-ui' && (
          <ThreeXUIConfigForm
            onSubmit={handleFormSubmit}
          />
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor will be set dynamically
  },
  content: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
});
