import { ConfigFormData } from '@/lib/types';
import { customFetch } from '@/lib/utils';

/**
 * X-UI面板API验证接口响应类型
 */
interface XUILoginResponse {
  success: boolean;
  message?: string;
  data?: any;
}

/**
 * 验证X-UI面板API连接
 * @param formData 配置表单数据
 * @param certFingerprints 可选的证书指纹数组
 * @returns Promise<boolean> 返回验证是否成功
 */
export async function validateXUIConnection(
  formData: ConfigFormData,
  certFingerprints?: string[]
): Promise<boolean> {
  try {
    const { protocol, url, username, password } = formData;
    
    if (!url || !username || !password) {
      throw new Error('URL, username and password are required');
    }

    // 构建完整的API URL
    const baseUrl = `${protocol}://${url}`;
    const loginUrl = `${baseUrl}/login`;

    // 准备请求头
    const headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');

    // 准备请求体 - X-UI使用用户名密码进行验证
    const urlencoded = new URLSearchParams();
    urlencoded.append('username', username);
    urlencoded.append('password', password);

    const requestOptions: RequestInit = {
      method: 'POST',
      headers: headers,
      body: urlencoded,
      redirect: 'follow'
    };

    // 发送请求
    const response = await customFetch(loginUrl, requestOptions, certFingerprints);
    
    // 检查HTTP状态
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    // 解析响应
    let result: XUILoginResponse;
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('application/json')) {
      result = await response.json();
    } else {
      // 如果不是JSON响应，尝试解析文本
      const text = await response.text();
      try {
        result = JSON.parse(text);
      } catch {
        // 如果无法解析为JSON，假设成功（某些面板可能返回非JSON响应）
        result = { success: true };
      }
    }

    // 检查业务逻辑成功标志
    return result.success === true;

  } catch (error) {
    console.error('X-UI connection validation failed:', error);

    // 提供更详细的错误信息
    if (error instanceof TypeError && error.message.includes('fetch')) {
      console.error('Fetch error details:', {
        message: error.message,
        stack: error.stack,
        formData: { url: formData.url, protocol: formData.protocol },
        certFingerprints: certFingerprints?.length || 0
      });
    }

    return false;
  }
}

/**
 * 构建X-UI面板的完整配置URL
 * @param protocol 协议类型
 * @param url 基础URL
 * @returns 完整的面板URL
 */
export function buildXUIUrl(protocol: string, url: string): string {
  return `${protocol}://${url}`;
}

/**
 * 验证X-UI配置表单数据
 * @param formData 表单数据
 * @returns 验证结果和错误信息
 */
export function validateXUIFormData(formData: ConfigFormData): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!formData.name?.trim()) {
    errors.push('Configuration name is required');
  }

  if (!formData.url?.trim()) {
    errors.push('URL is required');
  } else {
    try {
      // 验证URL格式
      new URL(`${formData.protocol}://${formData.url}`);
    } catch {
      errors.push('Invalid URL format');
    }
  }

  if (!formData.username?.trim()) {
    errors.push('Username is required');
  }

  if (!formData.password?.trim()) {
    errors.push('Password is required');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
