import { clsx, type ClassValue } from 'clsx';
import * as Crypto from 'expo-crypto';
import { fetch as sslFetch } from 'react-native-ssl-pinning';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * 从证书字符串中提取SHA256指纹并转换为base64编码的数组格式
 * @param certString 证书字符串，可以包含多个证书
 * @returns Promise<string[]> 返回SHA256指纹数组，格式为 ["sha256//base64hash", ...]
 */
export async function extractCertFingerprints(certString: string): Promise<string[]> {
  if (!certString || !certString.trim()) {
    return [];
  }

  const fingerprints: string[] = [];

  // 匹配所有证书块（包括证书链）
  const certRegex = /-----BEGIN CERTIFICATE-----[\s\S]*?-----END CERTIFICATE-----/g;
  const certMatches = certString.match(certRegex);

  if (!certMatches) {
    return [];
  }

  for (const certMatch of certMatches) {
    try {
      // 移除证书头尾和换行符，获取纯base64内容
      const certContent = certMatch
        .replace(/-----BEGIN CERTIFICATE-----/g, '')
        .replace(/-----END CERTIFICATE-----/g, '')
        .replace(/\s/g, '');

      // 将base64转换为二进制数据
      const binaryData = atob(certContent);
      const uint8Array = new Uint8Array(binaryData.length);
      for (let i = 0; i < binaryData.length; i++) {
        uint8Array[i] = binaryData.charCodeAt(i);
      }

      // 计算SHA256哈希
      const hashBuffer = await Crypto.digest(Crypto.CryptoDigestAlgorithm.SHA256, uint8Array);

      // 将ArrayBuffer转换为base64
      const hashArray = new Uint8Array(hashBuffer);
      const hashBase64 = btoa(String.fromCharCode(...hashArray));

      // 格式化为标准的证书指纹格式
      fingerprints.push(`sha256//${hashBase64}`);
    } catch (error) {
      console.warn('Failed to extract fingerprint from certificate:', error);
      // 继续处理其他证书，不中断整个过程
    }
  }

  return fingerprints;
}

// 新的SSL固定库不需要这些类型定义，因为它使用不同的API

/**
 * 封装的fetch函数，支持可选的SSL证书固定
 * @param url 请求URL
 * @param options 请求选项
 * @param certs 可选的证书数组，如果提供则使用SSL固定
 * @returns Promise<any> 返回响应对象
 */
export async function customFetch(
  url: string,
  options: RequestInit = {},
  certs?: string[]
): Promise<any> {
  if (certs && certs.length > 0) {
    try {
      const method = (options.method?.toUpperCase() as HTTPMethod) || 'GET';

      const sslOptions: SSLPinningOptions = {
        method: method,
        timeoutInterval: 10000, // 默认10秒超时
        pkPinning: true,
        sslPinning: {
          certs: certs
        },
        headers: {
          'Accept': 'application/json; charset=utf-8',
          'Access-Control-Allow-Origin': '*',
          'e_platform': 'mobile',
          ...(options.headers as Record<string, string> || {})
        },
        body: options.body ? (typeof options.body === 'string' ? options.body : JSON.stringify(options.body)) : undefined
      };

      return sslFetch(url, sslOptions);
    } catch (error) {
      console.warn('Failed to use SSL pinning, falling back to native fetch:', error);
      // 如果SSL固定失败，回退到原生fetch
      return fetch(url, {
        ...options,
        headers: {
          'Accept': 'application/json; charset=utf-8',
          'Access-Control-Allow-Origin': '*',
          'e_platform': 'mobile',
          ...options.headers
        }
      });
    }
  } else {
    // 使用原生fetch
    return fetch(url, {
      ...options,
      headers: {
        'Accept': 'application/json; charset=utf-8',
        'Access-Control-Allow-Origin': '*',
        'e_platform': 'mobile',
        ...options.headers
      }
    });
  }
}
